export interface Product {
  id: string
  slug: string
  title: string
  description: string
  coverImage: string
  category: string
  tags: string[]
  rating: number
  commentCount: number
  packages: Package[]
  customFields?: CustomField[]
  dropdowns?: Dropdown[]
  featured: boolean
  popular: boolean
}

export interface Package {
  id: string
  name: string
  price: number
  originalPrice?: number
  discount?: number
  image: string
  description?: string
  digitalCodes?: DigitalCode[]
  hasDigitalCodes?: boolean
  availableCodesCount?: number
}

export interface DigitalCode {
  id: string
  key: string
  used: boolean
  assignedToOrderId: string | null
  assignedAt?: string
  viewedCount?: number
  lastViewedAt?: string
}

export interface CustomField {
  id: string
  label: string
  type: "text" | "email" | "number"
  required: boolean
  placeholder: string
}

export interface Dropdown {
  id: string
  label: string
  options: DropdownOption[]
  required: boolean
}

export interface DropdownOption {
  value: string
  label: string
}

export interface User {
  id: string
  email: string
  name: string
  role: "admin" | "distributor" | "user"
  walletBalance: number
  avatar?: string
}

export interface Order {
  id: string
  userId: string
  productId: string
  packageId: string
  amount: number
  status: "pending" | "completed" | "failed"
  createdAt: string
  customData?: Record<string, any>
  digitalCode?: {
    key: string
    revealed: boolean
    revealedAt?: string
    viewCount: number
  }
}

export interface BannerSlide {
  id: string
  title: string
  subtitle?: string
  image: string
  linkType: "product" | "collection" | "custom" | "none"
  linkValue?: string // product slug, collection name, or custom URL
  active: boolean
  order: number
}

export interface HomepageSection {
  id: string
  title: string
  emoji?: string
  productIds: string[]
  order: number
  active: boolean
}

export interface HomepageConfig {
  banners: BannerSlide[]
  sections: HomepageSection[]
}
