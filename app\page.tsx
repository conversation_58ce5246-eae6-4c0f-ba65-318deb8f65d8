import BannerSlider from "./components/BannerSlider"
import CategorySection from "./components/CategorySection"
import { mockProducts, mockBanners, mockHomepageSections } from "./data/mockData"

export default function HomePage() {
  // TODO: Replace with actual user role from Supabase auth
  const userRole = "user"

  // Get active homepage sections sorted by order
  const activeSections = mockHomepageSections.filter((section) => section.active).sort((a, b) => a.order - b.order)

  return (
    <div className="min-h-screen">
      {/* Hero Banner */}
      <section className="container mx-auto px-4 py-8">
        <BannerSlider banners={mockBanners} />
      </section>

      {/* Dynamic Product Sections */}
      <div className="container mx-auto px-4 py-8">
        {activeSections.map((section) => {
          const sectionProducts = mockProducts.filter((product) => section.productIds.includes(product.id))

          if (sectionProducts.length === 0) return null

          return (
            <CategorySection
              key={section.id}
              title={`${section.emoji || ""} ${section.title}`.trim()}
              products={sectionProducts}
              userRole={userRole}
            />
          )
        })}
      </div>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 py-16 mt-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">مستعد لترقية تجربة الألعاب؟</h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            انضم إلى آلاف اللاعبين الراضين الذين يثقون في متجر بنتاكون لاحتياجاتهم في الألعاب. تسليم سريع، مدفوعات آمنة،
            ودعم على مدار الساعة.
          </p>
          <button className="btn-primary text-lg px-8 py-4">ابدأ التسوق الآن</button>
        </div>
      </section>
    </div>
  )
}
