"use client"

import { useState } from "react"
import { Wallet, Plus, MessageCircle, Clock, CheckCircle, XCircle, Key, Eye, EyeOff, <PERSON><PERSON>, Check } from "lucide-react"
import { mockOrders, mockProducts } from "../data/mockData"

export default function WalletPage() {
  // TODO: Replace with actual user data from Supabase auth
  const mockUser = {
    id: "3",
    name: "أحمد محمد",
    email: "<EMAIL>",
    walletBalance: 150.0,
  }

  const [showTopUpModal, setShowTopUpModal] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [showCodeModal, setShowCodeModal] = useState(false)
  const [revealedCodes, setRevealedCodes] = useState<Record<string, boolean>>({})
  const [copiedCodes, setCopiedCodes] = useState<Record<string, boolean>>({})

  // Get user's orders
  const userOrders = mockOrders.filter((order) => order.userId === mockUser.id)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case "pending":
        return <Clock className="w-5 h-5 text-yellow-400" />
      case "failed":
        return <XCircle className="w-5 h-5 text-red-400" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400 bg-green-400/10"
      case "pending":
        return "text-yellow-400 bg-yellow-400/10"
      case "failed":
        return "text-red-400 bg-red-400/10"
      default:
        return "text-gray-400 bg-gray-400/10"
    }
  }

  const handleTopUpWhatsApp = () => {
    const message = `مرحباً! أريد شحن محفظتي. بريد حسابي: ${mockUser.email}`
    const whatsappUrl = `https://wa.me/1234567890?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const handleShowCode = (order: any) => {
    setSelectedOrder(order)
    setShowCodeModal(true)

    // TODO: Track code view in Supabase
    // TODO: Increment view count for this order's digital code
  }

  const handleRevealCode = (orderId: string) => {
    setRevealedCodes((prev) => ({ ...prev, [orderId]: true }))

    // TODO: Update Supabase with reveal timestamp and increment view count
    // TODO: Track user interaction for security purposes
  }

  const handleCopyCode = async (code: string, orderId: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedCodes((prev) => ({ ...prev, [orderId]: true }))

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopiedCodes((prev) => ({ ...prev, [orderId]: false }))
      }, 2000)

      // TODO: Track copy action in Supabase for security monitoring
    } catch (err) {
      console.error("Failed to copy code:", err)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          محفظتي
        </h1>
        <p className="text-gray-400 text-lg">إدارة رصيدك وعرض تاريخ الطلبات</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Wallet Balance Card */}
        <div className="lg:col-span-1">
          <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-xl p-6 text-white mb-6 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2 space-x-reverse">
                <Wallet className="w-6 h-6" />
                <span className="text-lg font-semibold">رصيد المحفظة</span>
              </div>
            </div>
            <div className="text-3xl font-bold mb-6">${mockUser.walletBalance.toFixed(2)}</div>
            <button
              onClick={() => setShowTopUpModal(true)}
              className="w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse shadow-lg"
            >
              <Plus className="w-5 h-5" />
              <span>شحن المحفظة</span>
            </button>
          </div>

          {/* Quick Stats */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl">
            <h3 className="text-lg font-semibold mb-4">إحصائيات سريعة</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">إجمالي الطلبات</span>
                <span className="font-semibold">{userOrders.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">مكتملة</span>
                <span className="font-semibold text-green-400">
                  {userOrders.filter((o) => o.status === "completed").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">قيد الانتظار</span>
                <span className="font-semibold text-yellow-400">
                  {userOrders.filter((o) => o.status === "pending").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">طلبات رقمية</span>
                <span className="font-semibold text-blue-400">{userOrders.filter((o) => o.digitalCode).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">إجمالي المصروف</span>
                <span className="font-semibold">
                  ${userOrders.reduce((sum, order) => sum + order.amount, 0).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Order History */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
            <div className="p-6 border-b border-gray-700/50">
              <h2 className="text-2xl font-bold">تاريخ الطلبات</h2>
            </div>

            <div className="p-6">
              {userOrders.length > 0 ? (
                <div className="space-y-4">
                  {userOrders.map((order) => {
                    const product = mockProducts.find((p) => p.id === order.productId)
                    const package_ = product?.packages.find((p) => p.id === order.packageId)

                    return (
                      <div
                        key={order.id}
                        className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 space-x-reverse mb-1">
                              <h3 className="font-semibold text-lg">{product?.title || "منتج غير معروف"}</h3>
                              {order.digitalCode && (
                                <div className="flex items-center space-x-1 space-x-reverse">
                                  <Key className="w-4 h-4 text-blue-400" />
                                  <span className="text-xs text-blue-400">كود رقمي</span>
                                </div>
                              )}
                            </div>
                            <p className="text-gray-400 text-sm mb-2">الحزمة: {package_?.name || "حزمة غير معروفة"}</p>
                            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400">
                              <span>طلب #{order.id}</span>
                              <span>{new Date(order.createdAt).toLocaleDateString("ar")}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xl font-bold mb-2">${order.amount.toFixed(2)}</div>
                            <div
                              className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-lg text-sm ${getStatusColor(order.status)}`}
                            >
                              {getStatusIcon(order.status)}
                              <span>
                                {order.status === "completed"
                                  ? "مكتمل"
                                  : order.status === "pending"
                                    ? "قيد الانتظار"
                                    : "فاشل"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Digital Code Section */}
                        {order.digitalCode && order.status === "completed" && (
                          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mt-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2 space-x-reverse">
                                <Key className="w-4 h-4 text-blue-400" />
                                <span className="text-sm font-semibold text-blue-400">كود رقمي متاح</span>
                              </div>
                              <button
                                onClick={() => handleShowCode(order)}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg text-sm font-semibold transition-all duration-300 flex items-center space-x-1 space-x-reverse"
                              >
                                <Eye className="w-4 h-4" />
                                <span>عرض الكود</span>
                              </button>
                            </div>
                            {order.digitalCode.revealed && (
                              <p className="text-xs text-blue-300 mt-2">
                                تم عرض الكود {order.digitalCode.viewCount} مرة
                                {order.digitalCode.revealedAt &&
                                  ` • آخر عرض: ${new Date(order.digitalCode.revealedAt).toLocaleDateString("ar")}`}
                              </p>
                            )}
                          </div>
                        )}

                        {/* Custom Data */}
                        {order.customData && Object.keys(order.customData).length > 0 && (
                          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-3 mt-3 border border-gray-600/50">
                            <h4 className="text-sm font-semibold mb-2 text-gray-300">تفاصيل الطلب:</h4>
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              {Object.entries(order.customData).map(([key, value]) => (
                                <div key={key} className="flex justify-between">
                                  <span className="text-gray-400 capitalize">{key.replace("-", " ")}:</span>
                                  <span className="text-white">{value}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">📦</div>
                  <h3 className="text-xl font-semibold mb-2">لا توجد طلبات بعد</h3>
                  <p className="text-gray-400 mb-6">ابدأ التسوق لرؤية تاريخ طلباتك هنا</p>
                  <button className="btn-primary">تصفح المنتجات</button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Top Up Modal */}
      {showTopUpModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl p-6 max-w-md w-full border border-gray-700/50 shadow-2xl">
            <h3 className="text-2xl font-bold mb-4">شحن المحفظة</h3>
            <p className="text-gray-400 mb-6">
              تواصل مع فريق الدعم عبر الواتساب لإضافة أموال إلى محفظتك. سنقوم بمعالجة طلبك بسرعة وأمان.
            </p>

            <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 mb-6 border border-gray-600/50">
              <h4 className="font-semibold mb-2">معلومات حسابك:</h4>
              <p className="text-sm text-gray-300">البريد الإلكتروني: {mockUser.email}</p>
              <p className="text-sm text-gray-300">الرصيد الحالي: ${mockUser.walletBalance.toFixed(2)}</p>
            </div>

            <div className="flex space-x-3 space-x-reverse">
              <button
                onClick={handleTopUpWhatsApp}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 space-x-reverse shadow-lg"
              >
                <MessageCircle className="w-5 h-5" />
                <span>تواصل عبر الواتساب</span>
              </button>
              <button onClick={() => setShowTopUpModal(false)} className="btn-secondary">
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Digital Code Modal */}
      {showCodeModal && selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl p-6 max-w-lg w-full border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold">تفاصيل الطلب والمحتوى الرقمي</h3>
              <button
                onClick={() => setShowCodeModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            {/* Order Details */}
            <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 mb-6 border border-gray-600/50">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">اسم المنتج:</span>
                  <p className="font-semibold">{mockProducts.find((p) => p.id === selectedOrder.productId)?.title}</p>
                </div>
                <div>
                  <span className="text-gray-400">المبلغ:</span>
                  <p className="font-semibold">${selectedOrder.amount.toFixed(2)}</p>
                </div>
                <div>
                  <span className="text-gray-400">التاريخ:</span>
                  <p className="font-semibold">{new Date(selectedOrder.createdAt).toLocaleString("ar")}</p>
                </div>
                <div>
                  <span className="text-gray-400">الحالة:</span>
                  <div
                    className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded text-xs ${getStatusColor(selectedOrder.status)}`}
                  >
                    {getStatusIcon(selectedOrder.status)}
                    <span>✅ مكتمل</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Digital Code Section */}
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 space-x-reverse mb-3">
                <Key className="w-5 h-5 text-blue-400" />
                <h4 className="font-semibold text-blue-400">كود رقمي</h4>
              </div>

              <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-3 mb-4 border border-gray-600/50">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-400">الكود:</span>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    {!revealedCodes[selectedOrder.id] ? (
                      <button
                        onClick={() => handleRevealCode(selectedOrder.id)}
                        className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-xs font-semibold transition-all duration-300 flex items-center space-x-1 space-x-reverse"
                      >
                        <Eye className="w-3 h-3" />
                        <span>عرض</span>
                      </button>
                    ) : (
                      <button
                        onClick={() => setRevealedCodes((prev) => ({ ...prev, [selectedOrder.id]: false }))}
                        className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs font-semibold transition-all duration-300 flex items-center space-x-1 space-x-reverse"
                      >
                        <EyeOff className="w-3 h-3" />
                        <span>إخفاء</span>
                      </button>
                    )}
                  </div>
                </div>

                <div className="font-mono text-lg bg-gray-900/50 rounded p-3 border border-gray-600/50">
                  {revealedCodes[selectedOrder.id] ? (
                    <div className="flex items-center justify-between">
                      <span className="text-green-400 font-bold">{selectedOrder.digitalCode.key}</span>
                      <button
                        onClick={() => handleCopyCode(selectedOrder.digitalCode.key, selectedOrder.id)}
                        className="bg-gray-700 hover:bg-gray-600 text-white p-2 rounded transition-all duration-300"
                        title="نسخ الكود"
                      >
                        {copiedCodes[selectedOrder.id] ? (
                          <Check className="w-4 h-4 text-green-400" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  ) : (
                    <span className="text-gray-500">••••••••••••••••</span>
                  )}
                </div>
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                <h5 className="font-semibold text-yellow-400 mb-2">كيفية الاستخدام:</h5>
                <p className="text-sm text-yellow-200">
                  ادخل إلى اللعبة، اختر "استرداد كود"، ثم أدخل الكود أعلاه. تأكد من إدخال الكود بدقة لتجنب أي أخطاء.
                </p>
              </div>

              {selectedOrder.digitalCode.viewCount > 0 && (
                <div className="mt-3 text-xs text-gray-400">
                  تم عرض هذا الكود {selectedOrder.digitalCode.viewCount} مرة
                  {selectedOrder.digitalCode.revealedAt &&
                    ` • آخر عرض: ${new Date(selectedOrder.digitalCode.revealedAt).toLocaleString("ar")}`}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
